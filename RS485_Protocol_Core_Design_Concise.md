# RS485 Communication Protocol Core Design

## Executive Summary

This document provides a concise overview of the RS485 driver protocol's core design philosophy, focusing on how **payload data transmission** between User PC and RS485 Driver is efficiently managed to satisfy the requirements specified in the design document - "RS485 Communication Software Protocol_v1.1".

###  **Key Progress for Management Review**

**DESIGN PHASE:**
- **Architecture**: Single .exe with embedded UMDF 2.0 + FTDI VCP functionality
- **Protocol Core**: 12-byte payload-focused buffer management
- **API Design**: 5 categories with automatic function code routing
- **Implementation Priority**: S-series and U-series commands identified for first phase

**READY FOR IMPLEMENTATION:**
- **Phase 1**: S001/S002 (system config) + U001-U006 (user config) - **PRIORITY**
- **Phase 2**: A001-A005 (data queries) - **SECOND**
- **Phase 3**: W001/W002 (AI model data) - **THIRD**

**TECHNICAL SPECIFICATIONS:**
- **Buffer Allocation**: 60 bytes uplink + 120 bytes downlink (payload-only storage)
- **Error Handling**: Automatic CRC retry + FTDI error inheritance
- **Deployment**: Zero-installation single executable solution

## 1. Protocol Core Philosophy

### 1.1 The Central Focus: 12-Byte Payload Management

The entire RS485 communication protocol is built around efficiently handling **12-byte payload data**, which contains all meaningful communication information between PC and slave devices.

**Frame Structure (16 bytes total):**
```
┌─────────┬─────────┬──────────────────────┬─────────┬─────────┐
│ Header  │ ID Byte │    Payload (12B)     │  CRC8   │Trailer  │
│  0xAA   │ Func+Addr│  Key(4B) + Value(8B) │ 1 byte  │  0x0D   │
│ 1 byte  │ 1 byte  │     CORE DATA        │ 1 byte  │ 1 byte  │
└─────────┴─────────┴──────────────────────┴─────────┴─────────┘
```

**Key Design Principle:** Only the 12-byte payload contains effective information. The driver's buffer management system exclusively focuses on these payload segments.

### 1.2 Function Code to API Category Mapping

The protocol's intelligence lies in automatic routing based on function codes embedded in the ID byte:

| Function Code | API Category | Purpose | Payload Content |
|:-------------:|:-------------|:--------|:----------------|
| **0b111** | Master Broadcasting + Assign Data | S/U/W-series commands | Command Key + Configuration Value |
| **0b110** | Master Request | A-series queries | Request Key + Reserved |
| **0b010** | Slave Response (Assign ACK) | Acknowledgments | Status + Response Data |
| **0b001** | Slave Response (Data) | Query responses | Response Key + Data |
| **0b000** | Error Handle | Automatic retry | Error Code + Retry Info |

## 2. Buffer Management Architecture

### 2.1 Driver-Managed Payload Buffers

**Buffer Specifications:**
- **Uplink Buffer**: 5 slots × 12 bytes = 60 bytes (PC → Device)
- **Downlink Buffer**: 10 slots × 12 bytes = 120 bytes (Device → PC)
- **FIFO Guarantee**: Strict First-In-First-Out ordering maintained
- **Buffer Flag Checking**: Mandatory verification before transmission/storage

**Critical Buffer Flag Process:**
1. **Before Sending**: Check uplink buffer flag to ensure space availability
2. **Before Storing**: Check downlink buffer flag to prevent overflow
3. **Overflow Policies**: Configurable (discard oldest/newest or trigger error)

### 2.2 Why DeviceIoControl() is Not Directly Exposed

The high-level API abstracts DeviceIoControl() calls for several strategic reasons:

**Implementation Strategy:**
```
User Application
       ↓
High-Level API (configureSystemSettings, requestData, etc.)
       ↓
Internal DeviceIoControl() calls with IOCTL codes
       ↓
Windows Driver (UMDF 2.0 integrated)
       ↓
FTDI VCP functionality (embedded)
       ↓
USB-RS485 Hardware
```

**Why This Approach Works:**
1. **Abstraction Benefits**: Users interact with domain-specific functions instead of generic IOCTL codes
2. **Automatic Buffer Management**: Each API call internally performs buffer flag checking
3. **Function Code Routing**: API automatically determines correct function codes based on command type
4. **Error Handling**: Comprehensive error management without exposing Windows driver complexity
5. **Future Extensibility**: New functionality can be added without changing user interface

## 3. Management APIs vs. Protocol APIs

### 3.1 Management APIs (FTDI-Style)

These APIs handle driver lifecycle and buffer control, similar to standard FTDI RS485 drivers:

**Port Management:**
- `openPort()`, `closePort()`, `isPortOpen()`
- `enumerateDevices()`, `getPortInfo()`

**Buffer Control:**
- `getBufferStatus()`, `checkUplinkBufferFlag()`, `checkDownlinkBufferFlag()`
- `clearBuffer()`, `setBufferOverflowPolicy()`

**Hardware Status:**
- `getHardwareStatus()`, `getBaudRate()`
- `getPerformanceMetrics()` - Communication statistics and throughput metrics
- `getLineStatus()` - Real-time RS485 bus and hardware status

### 3.2 Protocol APIs (ID-Based Design)

These APIs implement the core ZES protocol functionality, automatically routing based on command IDs:

**🔥 PRIORITY: System Configuration (S-series) - FIRST IMPLEMENTATION**

| Command | Description | Value Range | ASCII Key | API Call |
|---------|-------------|-------------|-----------|----------|
| **S001** | Set RS485 slave address | 1-31 | 0x53303031 | `configureSystemSettings(0x53303031, slaveAddress)` |
| **S002** | Set baud rate | 9600, 19200, 38400, 57600, 115200 | 0x53303032 | `configureSystemSettings(0x53303032, baudRate)` |

**🔥 PRIORITY: User Configuration (U-series) - FIRST IMPLEMENTATION**

| Command | Description | Value Range | ASCII Key | API Call |
|---------|-------------|-------------|-----------|----------|
| **U001** | Set SEL detection threshold | 40-500 milliampere | 0x55303031 | `configureUserSettings(0x55303031, threshold)` |
| **U002** | Set SEL maximum amplitude threshold | 1000-2000 milliampere | 0x55303032 | `configureUserSettings(0x55303032, maxAmplitude)` |
| **U003** | Set number of SEL detections before power cycle | 1-5 | 0x55303033 | `configureUserSettings(0x55303033, detectionCount)` |
| **U004** | Set power cycle duration | 200, 400, 600, 800, or 1000 milliseconds | 0x55303034 | `configureUserSettings(0x55303034, cycleDuration)` |
| **U005** | Enable/disable GPIO input functions | See GPIO Value Packing | 0x55303035 | `configureUserSettings(0x55303035, gpioInputConfig)` |
| **U006** | Enable/disable GPIO output functions | See GPIO Value Packing | 0x55303036 | `configureUserSettings(0x55303036, gpioOutputConfig)` |

**Application Queries (A-series) - SECOND IMPLEMENTATION**

| Command | Description | Response Data | ASCII Key | API Call |
|---------|-------------|---------------|-----------|----------|
| **A001** | Request SEL event log | JSON structure with event records | 0x41303031 | `requestData(slaveAddress, 0x41303031)` |
| **A002** | Request device status | Status flags (16-bit) | 0x41303032 | `requestData(slaveAddress, 0x41303032)` |
| **A003** | Request firmware version | Version string | 0x41303033 | `requestData(slaveAddress, 0x41303033)` |
| **A004** | Request system statistics | JSON structure with statistics | 0x41303034 | `requestData(slaveAddress, 0x41303034)` |
| **A005** | Request current configuration | JSON structure with all current settings | 0x41303035 | `requestData(slaveAddress, 0x41303035)` |

**Model Data (W-series) - THIRD IMPLEMENTATION**

| Operation | Description | Parameters | ASCII Key | API Call |
|-----------|-------------|------------|-----------|----------|
| **W001** | Write model data to FRAM | Memory address, data bytes | 0x57303031 | `modelDataOperation(address, data, true)` |
| **W002** | Read model data from FRAM | Memory address, length | 0x57303032 | `modelDataOperation(address, data, false)` |

### 3.3 API Usage Examples

**Example 1: Initial Device Setup (S-series Priority Implementation)**
```cpp
// Step 1: Open port and check buffer status
driver.openPort("COM3");
BufferStatus status;
driver.getBufferStatus(status);

// Step 2: Set slave address (broadcast to address 0x00)
// Only one slave should be connected during address assignment
driver.configureSystemSettings(0x53303031, 5);  // S001: Set address to 5

// Step 3: Set baud rate
driver.configureSystemSettings(0x53303032, 115200);  // S002: Set to 115200 bps
```

**Example 2: User Configuration (U-series Priority Implementation)**
```cpp
// Configure SEL detection parameters
driver.configureUserSettings(0x55303031, 250);    // U001: 250mA threshold
driver.configureUserSettings(0x55303032, 1500);   // U002: 1500mA max amplitude
driver.configureUserSettings(0x55303033, 3);      // U003: 3 detections before cycle
driver.configureUserSettings(0x55303034, 600);    // U004: 600ms cycle duration

// Configure GPIO (using bit packing)
uint64_t enableCh0Input = 0 | (1 << 8);  // Channel 0, Enable
driver.configureUserSettings(0x55303035, enableCh0Input);  // U005: GPIO input
```

**Example 3: Data Query and Response Handling**
```cpp
// Request device status
driver.requestData(5, 0x41303032);  // A002: Request status from slave 5

// Receive response (non-blocking)
std::vector<uint8_t> responseData;
RS485Error result = driver.receiveSlaveResponse(5, responseData, false, 100);
if (result == RS485Error::SUCCESS) {
    // Process 16-bit status flags
    uint16_t statusFlags = *(uint16_t*)responseData.data();
}
```

### 3.4 GPIO Value Packing Format

For U005/U006 commands, the value parameter uses specific bit layout:
```cpp
// GPIO Value Packing Format:
// Lower 8 bits: Channel ID (0 or 1)
// Next 8 bits: Enable/Disable flag (0 = disable, 1 = enable)
// Upper 48 bits: Reserved (set to 0)

uint64_t packGPIOValue(uint8_t channel, bool enable) {
    return (channel & 0xFF) | ((enable ? 1 : 0) << 8);
}

// Examples:
uint64_t enableCh0Input = packGPIOValue(0, true);   // 0x0100
uint64_t disableCh1Output = packGPIOValue(1, false); // 0x0001
```

## 4. Error Handling Integration

### 4.1 FTDI Error Inheritance

The driver inherits and extends FTDI's error handling capabilities:
- **COM Port Errors**: Hardware disconnection, baud rate mismatch
- **Buffer Errors**: Overflow, underflow, timeout conditions
- **Protocol Errors**: CRC failures, invalid function codes, frame format errors

### 4.2 Automatic Retry Mechanism

**Function Code 0b000 Implementation:**
- **CRC Errors**: Automatic retry up to 3 attempts
- **Timeout Errors**: Configurable retry policies
- **Buffer Full**: Overflow policy application (discard/error)

## 5. Single Executable Architecture

### 5.1 Integrated UMDF 2.0 Framework

**Why Single .exe Works:**
- **UMDF Integration**: Windows User-Mode Driver Framework embedded within application
- **FTDI VCP Embedded**: No separate driver installation required
- **Protocol Processing**: ZES protocol handling integrated at application level
- **Buffer Management**: Driver-level buffer control within user-mode application

**Deployment Benefits:**
- **No Driver Installation**: Complete solution in single executable
- **System Stability**: User-mode implementation cannot crash system
- **Simplified Distribution**: Single file deployment model
- **Windows Compatibility**: Full Windows 10/11 support without admin privileges

## 6. Protocol Satisfaction Summary

This design satisfies all requirements from "RS485 Communication Software Protocol_v1.1":

✅ **ZES Data Link Protocol**: Complete implementation with frame packing/unpacking  
✅ **Buffer and FIFO**: Driver-managed 12-byte payload buffers with FIFO guarantee  
✅ **Error Handling**: CRC verification, timeout management, automatic retry  
✅ **API Categories**: Five distinct API categories as specified  
✅ **Master-Slave Control**: Proper bus control and collision avoidance  
✅ **FTDI Integration**: Embedded VCP functionality without separate installation  

**Core Design:** The protocol manages payload data transmission between User PC and RS485 Driver through buffer management, automatic function code routing, and error handling, while maintaining a simple, abstracted API interface for users.

## 7. Communication Flow Examples

### 7.1 Typical Device Configuration Sequence

```
PC (Master)                    FPGA Slave (Address 0→5)
     |                              |
     | S001 Broadcast (Addr=0)      |
     |----------------------------->| (Set address to 5)
     |                              | (Write to FRAM)
     | ACK Response (Func=0b010)    |
     |<-----------------------------|
     |                              |
     | U001 Command (Addr=5)        |
     |----------------------------->| (Set SEL threshold)
     | ACK Response (Func=0b010)    |
     |<-----------------------------|
     |                              |
     | A002 Request (Addr=5)        |
     |----------------------------->| (Request status)
     | Data Response (Func=0b001)   |
     |<-----------------------------|
```

### 7.2 Error Recovery Flow

```
PC (Master)                    FPGA Slave
     |                              |
     | Command with CRC Error       |
     |----------------------------->| (Detect CRC error)
     | Re-send Request (Code=0b000) |
     |<-----------------------------| (Request retry)
     | Retry Command (Attempt 1)    |
     |----------------------------->|
     | Success Response             |
     |<-----------------------------|
```

## 8. Implementation Strategy

### 8.1 Development Phases

**Phase 1: Core Infrastructure (S/U-series Priority)**
- ✅ UMDF 2.0 framework integration
- ✅ FTDI VCP embedding
- ✅ Buffer management system (12-byte payload focus)
- ✅ S001/S002 system configuration APIs
- ✅ U001-U006 user configuration APIs

**Phase 2: Data Communication (A-series)**
- 📋 A001-A005 request/response handling
- 📋 JSON response parsing
- 📋 Asynchronous data reception

**Phase 3: Advanced Features (W-series)**
- 📋 FRAM memory operations
- 📋 AI model data transfer
- 📋 Large data block handling

### 8.2 Key Design Decisions

**Why 12-byte Payload Focus:**
- Contains all meaningful communication data
- Simplified buffer management logic
- Faster data processing (no unnecessary frame overhead)

**Why Function Code Auto-routing:**
- Eliminates user complexity in protocol handling
- Automatic API category selection
- Built-in error handling per function type
- Future-proof extensibility

**Why Single .exe Architecture:**
- No driver installation complexity
- Simplified deployment and distribution
- User-mode stability (cannot crash system)
- Full Windows compatibility without admin rights

### 8.3 Technical Specifications

**Buffer Configuration:**
- Uplink: 5 × 12-byte slots = 60 bytes (PC → Device)
- Downlink: 10 × 12-byte slots = 120 bytes (Device → PC)
- FIFO guarantee with configurable overflow policies
- Real-time buffer status monitoring

**Communication Parameters:**
- Response window: 100ms (as per ZES protocol)
- Automatic retry: Up to 3 attempts for CRC errors
- Baud rates: 9600 to 115200 bps supported
- Maximum 31 slave devices on single bus

**Error Handling:**
- CRC8 verification on all frames
- Automatic timeout detection and recovery
- Comprehensive error categorization and reporting
- Integration with FTDI error handling patterns

## 9. Data Persistence and Configuration Management

### 9.1 FRAM-Based Configuration Storage

**Why FRAM Technology:**
- **Non-volatile Storage**: All S-series and U-series settings persist across power cycles
- **High Endurance**: Virtually unlimited read/write cycles (10^14+ operations)
- **Instant Write**: No write delays, immediate configuration effect
- **Data Retention**: 10+ years without power
- **Reliability**: No wear-out mechanisms like flash memory

**Configuration Persistence Strategy:**
```cpp
// All configuration commands automatically save to FRAM
driver.configureUserSettings(0x55303031, 250);  // U001: Saved to FRAM immediately
driver.configureSystemSettings(0x53303031, 5);  // S001: Address persists across reboots

// Retrieve current configuration from FRAM
driver.requestData(5, 0x41303035);  // A005: Read all current settings
```

### 9.2 Address Assignment Strategy

**Critical Design for S001 Implementation:**
```cpp
// STEP 1: Ensure single slave connection
std::vector<uint8_t> detectedAddresses;
driver.detectMultipleDevices(detectedAddresses);
if (detectedAddresses.size() > 1) {
    // ERROR: Multiple slaves detected - abort assignment
    return RS485Error::MULTIPLE_SLAVES_DETECTED;
}

// STEP 2: Broadcast address assignment to default address 0x00
driver.configureSystemSettings(0x53303031, newAddress);  // S001 broadcast

// STEP 3: Verify assignment success
driver.requestData(newAddress, 0x41303035);  // A005: Verify new address
```

**Address Conflict Prevention:**
- All AI-SLDAP boards ship with default address 0x00
- S001 command requires single slave connection
- Driver automatically detects multiple slave responses
- Address assignment failure triggers user alert

### 9.3 Configuration Validation and Recovery

**Built-in Validation:**
```cpp
// Range validation for all U-series commands
bool validateU001Threshold(uint64_t value) {
    return (value >= 40 && value <= 500);  // 40-500 mA range
}

bool validateU004Duration(uint64_t value) {
    return (value == 200 || value == 400 || value == 600 ||
            value == 800 || value == 1000);  // Specific values only
}
```

**Configuration Recovery:**
```cpp
// Read current configuration for backup/restore
driver.requestData(slaveAddr, 0x41303035);  // A005: Get current config
std::vector<uint8_t> configBackup;
driver.receiveSlaveResponse(slaveAddr, configBackup);

// Restore configuration if needed
// Parse JSON response and re-apply settings using U-series commands
```

## 10. Integration with Existing Systems

### 10.1 FTDI Driver Compatibility

**Seamless Integration Strategy:**
- **No Conflicts**: Embedded FTDI VCP functionality prevents driver conflicts
- **Standard Interface**: Maintains FTDI-style management API patterns
- **Hardware Compatibility**: Full support for USB-RS485-WE-1800-BT converter
- **Plug-and-Play**: Automatic device detection and configuration

### 10.2 Windows System Integration

**System Requirements:**
- **OS Support**: Windows 10/11 (x64)
- **Framework**: .NET Framework 4.8+ or .NET 6+
- **Privileges**: Standard user privileges (no admin required)
- **Dependencies**: Self-contained executable with embedded drivers

**Deployment Advantages:**
- **Single File**: Complete solution in one executable
- **No Installation**: Copy and run deployment model
- **Version Control**: Single file versioning and updates
- **Enterprise Ready**: MSI packaging available for corporate deployment
